"""
简单测试Lang<PERSON>hain Agent
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    print("🧪 开始简单测试...")
    
    # 测试导入
    print("📦 测试导入...")
    from app.agents.assistant_agent import AssistantAgent
    print("✅ 成功导入 AssistantAgent")
    
    # 测试创建Agent
    print("🤖 测试创建Agent...")
    agent = AssistantAgent()
    print("✅ 成功创建 AssistantAgent")
    print(f"Agent名称: {agent.name}")
    print(f"Agent类型: {type(agent).__name__}")
    
    # 测试简单查询
    print("📝 测试简单查询...")
    response = agent.process("你好", stream=False)
    print("✅ 成功处理查询")
    print(f"响应类型: {type(response)}")
    print(f"响应内容: {str(response)[:200]}...")
    
    print("🎉 所有测试通过！")
    
except Exception as e:
    print(f"❌ 测试失败: {str(e)}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
