"""
测试LangChain Agent功能
验证从自定义PPAO循环到标准Agent的改造是否成功
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
from app.agents.assistant_agent import AssistantAgent
from app.agents.testcase_agent import TestCaseAgent
from app.tools.basic_tools import CalculatorTool, TimeTool
from app.utils.logger import get_logger

logger = get_logger("test_langchain_agent")


def test_assistant_agent():
    """测试助手Agent的LangChain功能"""
    print("🧪 测试助手Agent...")
    
    try:
        # 创建助手Agent
        agent = AssistantAgent()
        print(f"✅ 成功创建助手Agent: {agent.name}")
        print(f"🔧 Agent类型: {type(agent).__name__}")
        print(f"🤖 基类: {type(agent).__bases__}")
        print(f"🛠️ 工具数量: {len(agent.custom_tools)}")
        
        # 测试简单查询
        print("\n📝 测试简单查询...")
        response = agent.process("你好，请介绍一下你自己", stream=False)
        print(f"📄 响应类型: {type(response)}")
        print(f"📝 响应内容: {response.get('response', '')[:200]}...")
        
        # 测试工具调用
        print("\n🔧 测试工具调用...")
        response = agent.process("请计算 15 + 27 的结果", stream=False)
        print(f"📄 响应类型: {type(response)}")
        print(f"🔧 是否使用工具: {response.get('used_tools', False)}")
        print(f"📝 响应内容: {response.get('response', '')[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 助手Agent测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_testcase_agent():
    """测试测试用例Agent的LangChain功能"""
    print("\n🧪 测试测试用例Agent...")
    
    try:
        # 创建测试用例Agent
        agent = TestCaseAgent()
        print(f"✅ 成功创建测试用例Agent: {agent.name}")
        print(f"🔧 Agent类型: {type(agent).__name__}")
        print(f"🤖 基类: {type(agent).__bases__}")
        print(f"🛠️ 工具数量: {len(agent.custom_tools)}")
        
        # 测试需求分析
        print("\n📝 测试需求分析...")
        requirement = """
        用户登录功能需求：
        1. 用户可以通过用户名和密码登录
        2. 登录失败3次后账户锁定30分钟
        3. 支持记住登录状态功能
        """
        
        response = agent.process(f"请为以下需求编写测试用例：{requirement}", stream=False)
        print(f"📄 响应类型: {type(response)}")
        print(f"📝 响应内容: {response.get('response', '')[:300]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试用例Agent测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_stream_functionality():
    """测试流式功能"""
    print("\n🧪 测试流式功能...")
    
    try:
        agent = AssistantAgent()
        print(f"✅ 创建Agent用于流式测试")
        
        # 测试流式响应
        print("\n🌊 测试流式响应...")
        stream_generator = agent.process("请详细解释什么是人工智能", stream=True)
        
        chunk_count = 0
        total_content = ""
        
        for chunk in stream_generator:
            chunk_count += 1
            content = chunk.get('response_chunk', '')
            total_content += content
            
            if chunk_count <= 3:  # 只显示前3个块
                print(f"📦 块 {chunk_count}: {content[:50]}...")
            
            if chunk.get('is_last', False):
                print(f"🏁 流式完成，总块数: {chunk_count}")
                break
        
        print(f"📝 总内容长度: {len(total_content)} 字符")
        return True
        
    except Exception as e:
        print(f"❌ 流式功能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_langchain_features():
    """测试LangChain特有功能"""
    print("\n🧪 测试LangChain特有功能...")
    
    try:
        agent = AssistantAgent()
        
        # 检查Agent Executor
        print(f"🤖 Agent Executor: {hasattr(agent, 'agent_executor')}")
        print(f"🔧 LangChain Tools: {len(agent.langchain_tools)}")
        print(f"📝 Prompt Template: {hasattr(agent, 'prompt_template')}")
        
        # 检查记忆系统
        print(f"💾 Custom Memory: {hasattr(agent, 'memory')}")
        print(f"💾 LangChain Memory: {hasattr(agent, 'langchain_memory')}")
        
        # 测试记忆功能
        print("\n🧠 测试记忆功能...")
        agent.process("我的名字是张三", stream=False)
        response = agent.process("你还记得我的名字吗？", stream=False)
        print(f"📝 记忆测试响应: {response.get('response', '')[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ LangChain功能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始LangChain Agent功能测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("助手Agent基础功能", test_assistant_agent()))
    test_results.append(("测试用例Agent基础功能", test_testcase_agent()))
    test_results.append(("流式功能", test_stream_functionality()))
    test_results.append(("LangChain特有功能", test_langchain_features()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！LangChain Agent改造成功！")
    else:
        print("⚠️ 部分测试失败，需要进一步调试")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
