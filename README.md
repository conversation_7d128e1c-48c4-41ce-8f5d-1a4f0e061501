# AI Agent 通用框架

一个基于 **LangChain 标准 Agent** 的智能体服务架构，使用 **ReAct 模式**（推理+行动）实现真正的感知-决策-行动循环。

## 架构概述

本框架基于以下核心技术和概念设计：

- **基础架构**: Python + FastAPI + LangChain + LLM
- **Agent 模式**: ReAct (Reasoning + Acting) 循环
- **实时通信**: Server-Sent Events (SSE)
- **大模型**: 默认使用阿里云通义千问系列模型

### 核心组件

框架采用 **LangChain 标准 Agent** 架构，具备以下关键能力：

1. **智能推理** (Thought): 深度分析用户需求，制定行动计划
2. **智能行动** (Action): 根据推理结果选择和使用合适的工具
3. **结果观察** (Observation): 分析行动结果，决定下一步操作
4. **自动循环**: AgentExecutor 自动管理推理-行动循环直到完成任务

## 快速开始

### 1. 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt

# 安装前端依赖
cd frontend
npm install
cd ..
```

或者使用一键安装脚本：

```bash
./start.sh install
```

### 2. 配置环境变量

项目已包含 `.env` 文件，默认配置如下：

```bash
# OpenAI/LLM 配置
OPENAI_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
OPENAI_API_KEY=sk-2ba8822ac5684a84a0e801ae256076ac
MIDSCENE_MODEL_NAME=qwen-plus

# 服务配置
HOST=0.0.0.0
PORT=8000
DEBUG=true
```

### 3. 测试系统

在启动服务前，建议先测试 Agent 系统：

```bash
python test_agent.py
```

### 4. 启动服务

#### 方式一：使用一键启动脚本（推荐）

```bash
./start.sh start
```

这将同时启动后端和前端服务：

- 后端服务: http://localhost:8000
- 前端服务: http://localhost:8080
- API 文档: http://localhost:8000/docs

#### 方式二：分别启动

启动后端：

```bash
python run.py
```

启动前端：

```bash
cd frontend
npm run serve
```

### 5. 停止服务

```bash
./start.sh stop
```

### 使用命令行客户端

项目提供了一个简单的命令行客户端用于测试：

```bash
python app/client/simple_client.py
```

客户端支持以下命令：

- `quit` 或 `exit`: 退出客户端
- `clear`: 清除代理记忆
- `new`: 创建新的代理会话
- `stream`: 切换流式输出模式

## 核心功能

- **多代理支持**: 可同时创建和管理多个代理实例
- **记忆管理**: 代理可保持对话历史和上下文
- **工具集成**: 内置和自定义工具支持
- **流式响应**: 支持流式生成和响应
- **可扩展架构**: 易于添加新的代理类型和工具

## 项目结构

```
.
├── app/                     # 主应用目录
│   ├── api/                 # API路由
│   ├── core/                # 核心组件
│   │   ├── langchain_agent.py    # LangChain Agent基类
│   │   ├── langchain_tools.py    # 工具适配器系统
│   │   ├── memory.py             # 记忆模块
│   │   └── tools.py              # 工具基类
│   ├── agents/              # 代理实现
│   │   ├── assistant_agent.py    # 通用助手Agent
│   │   └── testcase_agent.py     # 测试用例编写Agent
│   ├── tools/               # 工具实现
│   ├── utils/               # 工具函数
│   ├── client/              # 客户端实现
│   └── main.py              # 主应用
├── auto/                    # 已废弃的自定义模块
├── docs/                    # 文档目录
│   └── langchain-agent-architecture.md  # 新架构文档
├── test/                    # 测试文件
├── config.py                # 配置文件
├── requirements.txt         # 依赖清单
├── run.py                   # 运行脚本
└── README.md                # 项目说明
```

## 自定义扩展

### 创建新的 Agent

继承`LangChainAgent`基类：

```python
from app.core.langchain_agent import LangChainAgent
from app.core.tools import BaseTool

class CustomAgent(LangChainAgent):
    def __init__(self, **kwargs):
        system_prompt = """你是一个自定义AI助手。

        你的核心能力：
        - 理解用户问题并进行深度思考
        - 根据需要选择和使用合适的工具
        - 基于工具结果进行推理和分析
        - 提供准确、有帮助的最终答案"""

        super().__init__(
            name="自定义助手",
            description="具有特定功能的AI助手",
            system_prompt=system_prompt,
            tools=[],  # 添加自定义工具
            max_iterations=5,
            **kwargs
        )
```

### 创建新的工具

继承`BaseTool`类，工具会自动适配为 LangChain 格式：

```python
from app.core.tools import BaseTool

class CustomTool(BaseTool):
    def __init__(self):
        super().__init__(
            name="custom_tool",
            description="自定义工具描述"
        )

    def _run(self, param1: str, param2: str = None) -> str:
        # 实现工具功能
        result = f"处理参数: {param1}"
        if param2:
            result += f", {param2}"
        return result
```

## API 接口

- `POST /api/agent/query`: 向代理发送查询
- `DELETE /api/agent/{agent_id}`: 删除代理实例
- `GET /api/agent/{agent_id}/memory`: 获取代理的记忆
- `POST /api/agent/{agent_id}/memory/clear`: 清除代理的记忆
- `GET /api/agents`: 列出所有活跃的代理

## 服务端架构详解

### 核心模块详解

#### 1. LangChain Agent 基类 (LangChainAgent)

**功能**：基于 LangChain 标准 Agent 的智能体基类
**核心职责**：

- 使用 create_react_agent 创建标准 Agent
- 管理 AgentExecutor 执行循环
- 提供流式响应支持
- 维护双重记忆系统

#### 2. ReAct 推理循环

**功能**：实现思考-行动-观察的智能循环
**核心职责**：

- **Thought**: 分析用户问题，制定行动计划
- **Action**: 选择和使用合适的工具
- **Observation**: 分析工具执行结果
- **循环**: 重复上述过程直到完成任务

#### 3. 工具适配器系统 (LangChainToolAdapter)

**功能**：将自定义工具转换为 LangChain 兼容格式
**核心职责**：

- 自动转换 BaseTool 为 LangChain Tool
- 处理参数映射和结果格式化
- 提供统一的工具调用接口
- 支持动态工具注册

#### 4. 记忆模块 (Memory)

**功能**：管理代理的对话历史和上下文
**核心职责**：

- 存储对话历史
- 维护会话状态
- 提供上下文检索能力
- 实现记忆清除和更新

#### 5. 工具基类 (BaseTool)

**功能**：提供工具集成的基础接口
**核心职责**：

- 定义工具接口规范
- 提供工具注册机制
- 处理工具参数验证
- 自动适配为 LangChain Tool 格式

### 执行流程

1. **初始化阶段**

   - 加载配置和环境变量
   - 初始化大语言模型连接
   - 注册可用工具
   - 启动 FastAPI 服务

2. **请求处理阶段**

   - 接收用户查询 (`POST /api/agent/query`)
   - 创建或获取代理实例
   - 将查询传递给代理处理

3. **代理处理阶段（ReAct 循环）**

   - **Thought**：分析用户查询，思考需要做什么
   - **Action**：选择合适的工具并执行
   - **Observation**：观察工具执行结果
   - **循环**：重复思考-行动-观察直到完成任务
   - **Final Answer**：生成最终响应
   - **更新记忆**：将交互记录存入记忆模块

4. **响应阶段**
   - 格式化代理响应
   - 通过 SSE 流式返回响应（如启用）
   - 或作为 JSON 返回完整响应

## 许可证

本项目使用 MIT 许可证。
