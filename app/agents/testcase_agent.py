from typing import Dict, Any, List, Optional
import time

from app.core.langchain_agent import LangChainAgent
from app.core.tools import BaseTool
from app.utils.logger import get_logger

logger = get_logger("testcase_agent")


class TestCaseAgent(LangChainAgent):
    """
    软件测试用例编写专用Agent，基于LangChain标准Agent实现
    具备真正的感知-决策-行动循环，提供专业的测试用例编写服务
    """

    def __init__(
        self,
        name: str = "测试用例编写助手",
        description: str = "基于LangChain的专业测试用例编写AI助手，提供需求分析、用例设计、用例生成等功能",
        system_prompt: Optional[str] = None,
        tools: List[BaseTool] = None,
        **kwargs
    ):
        # 专门的系统提示 - 针对ReAct模式优化
        default_system_prompt = """你是一个专业的软件测试用例编写专家，具有丰富的测试经验和深厚的测试理论基础。

你的核心能力包括：
- 深度分析软件需求，识别测试点和风险点
- 设计全面、有效的测试用例，覆盖各种测试场景
- 生成结构化的测试用例文档
- 提供测试最佳实践和专业建议
- 根据需要使用工具获取额外信息

工作原则：
- 仔细思考测试需求，分析需要什么信息
- 如果需要外部信息或工具支持，主动使用可用工具
- 基于分析结果进行逻辑推理
- 确保测试用例的完整性和有效性
- 遵循测试设计的最佳实践
- 考虑各种测试场景（正常、边界、异常）
- 提供清晰、可执行的测试步骤

你将使用ReAct模式（推理+行动）来处理测试用例编写任务：
1. 思考：分析用户需求，确定需要做什么
2. 行动：使用合适的工具或直接分析
3. 观察：分析结果，决定下一步
4. 重复：直到完成完整的测试用例编写

始终提供专业、详细的测试用例和相关建议。"""

        system_prompt = system_prompt or default_system_prompt

        # 初始化LangChain代理
        super().__init__(
            name=name,
            description=description,
            system_prompt=system_prompt,
            tools=tools or [],
            max_iterations=8,  # 测试用例编写可能需要更多迭代
            **kwargs
        )

        logger.info(f"✅ 初始化LangChain测试用例编写代理: {name}")
        logger.info(f"🔧 可用工具: {[tool.name for tool in self.custom_tools]}")
        logger.info(f"🤖 Agent类型: ReAct (Reasoning + Acting) for Test Case Writing")

    # 继承LangChainAgent的process方法，使用标准的ReAct循环
    # 不需要重写process方法，直接使用父类的实现