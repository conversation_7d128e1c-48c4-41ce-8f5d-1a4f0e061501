from typing import Dict, Any, List, Optional

from app.core.langchain_agent import LangChainAgent
from app.core.tools import BaseTool
from app.tools.basic_tools import SearchTool, WeatherTool, CalculatorTool, TimeTool
from app.utils.logger import get_logger

logger = get_logger("assistant_agent")


class AssistantAgent(LangChainAgent):
    """
    通用AI助手代理，基于LangChain标准Agent实现
    提供信息查询、问答等功能，具备真正的感知-决策-行动循环
    """

    def __init__(
        self,
        name: str = "AI助手",
        description: str = "一个基于LangChain的智能AI助手，可以回答问题、提供信息和执行简单任务",
        system_prompt: Optional[str] = None,
        tools: List[BaseTool] = None,
        **kwargs
    ):
        # 默认系统提示 - 针对ReAct模式优化
        default_system_prompt = """你是一个智能AI助手，具备强大的推理和行动能力。

你的核心能力：
- 理解用户问题并进行深度思考
- 根据需要选择和使用合适的工具
- 基于工具结果进行推理和分析
- 提供准确、有帮助的最终答案

工作原则：
- 仔细思考每个问题，分析需要什么信息
- 如果需要外部信息，主动使用可用工具
- 基于获得的信息进行逻辑推理
- 提供清晰、准确的最终答案
- 承认不确定性，避免编造信息

交流风格：
- 友好、专业、有帮助
- 逻辑清晰，条理分明
- 适当解释推理过程"""

        system_prompt = system_prompt or default_system_prompt

        # 默认工具集
        if tools is None:
            try:
                default_tools = [
                    SearchTool(),
                    WeatherTool(),
                    CalculatorTool(),
                    TimeTool()
                ]
                tools = default_tools
                logger.info(f"初始化默认工具: {[tool.name for tool in tools]}")
            except Exception as e:
                logger.error(f"初始化工具失败: {e}")
                tools = []

        # 初始化LangChain代理
        super().__init__(
            name=name,
            description=description,
            system_prompt=system_prompt,
            tools=tools,
            max_iterations=5,  # ReAct模式的最大迭代次数
            **kwargs
        )

        logger.info(f"✅ 初始化LangChain助手代理: {name}, 工具数量: {len(self.custom_tools)}")
        logger.info(f"🔧 可用工具: {[tool.name for tool in self.custom_tools]}")
        logger.info(f"🤖 Agent类型: ReAct (Reasoning + Acting)")
    
    def custom_action(self, action_input: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行特定于助手的自定义动作
        """
        action_type = action_input.get("type")
        
        if action_type == "greeting":
            # 处理问候
            return {
                "response": f"你好！我是{self.name}，有什么可以帮到你的吗？"
            }
        elif action_type == "farewell":
            # 处理告别
            return {
                "response": "再见！如果有任何问题，随时回来问我。"
            }
        else:
            # 默认行为
            return {
                "response": "我不确定该如何处理这个请求。请问有什么具体的问题吗？"
            }
    
    def process_query(self, query: str) -> str:
        """
        处理特定查询并返回回答
        """
        return self.process(query) 