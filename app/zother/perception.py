from typing import Dict, Any, List
from langchain.chat_models.base import BaseChatModel
from langchain.prompts import ChatPromptTemplate
from langchain.schema import SystemMessage, HumanMessage

from app.core.memory import AgentMemory
from app.utils.json_fixer import fix_and_parse_json


class Perception:
    """
    感知模块：负责理解用户输入和上下文，提取关键信息
    """
    
    def __init__(self, llm: BaseChatModel, memory: AgentMemory):
        self.llm = llm
        self.memory = memory
        
        # 增强的系统提示，强调JSON格式
        self.system_prompt = """你是一个高级感知分析模块。分析用户输入，提取关键信息并以JSON格式返回。

必须包含以下字段:
- user_intent: 用户意图
- key_entities: 关键实体列表
- sentiment: 情感分析 (positive/neutral/negative)
- required_tools: 可能需要的工具列表
- context_connections: 与上下文的关联列表

严格以JSON格式返回，不要有任何其他文本。例如:
{
  "user_intent": "查询天气",
  "key_entities": ["天气", "今天"],
  "sentiment": "neutral",
  "required_tools": ["天气查询"],
  "context_connections": ["之前提到过地点"]
}"""
    
    def process(self, user_input: str) -> Dict[str, Any]:
        """
        处理用户输入，提取关键信息和意图
        """
        import time
        from app.utils.logger import logger

        perception_start = time.time()
        logger.info(f"👁️ 感知模块开始处理...")

        # 获取最近的对话历史（如果有）
        history_start = time.time()
        recent_history = self.memory.get_history_as_string()
        history_time = time.time() - history_start
        logger.info(f"📚 获取对话历史完成 - 历史长度: {len(recent_history)} 字符 (耗时: {history_time:.3f}s)")

        # 准备消息
        message_prep_start = time.time()
        messages = [
            SystemMessage(content=self.system_prompt),
            HumanMessage(content=f"用户输入: {user_input}\n\n历史对话上下文:\n{recent_history}")
        ]
        message_prep_time = time.time() - message_prep_start
        logger.info(f"📝 消息准备完成 - 消息数: {len(messages)} (耗时: {message_prep_time:.3f}s)")

        # 直接调用LLM
        llm_start = time.time()
        logger.info(f"🤖 开始调用LLM进行感知分析...")
        result = self.llm(messages)
        llm_time = time.time() - llm_start
        logger.info(f"✅ LLM调用完成 - 响应长度: {len(result.content)} 字符 (耗时: {llm_time:.3f}s)")

        # 将结果存储在上下文中
        parse_start = time.time()
        try:
            # 尝试解析JSON结果，使用json_fixer
            logger.info(f"🔧 开始解析JSON结果...")
            perception_data = fix_and_parse_json(result.content)
            if perception_data:
                parse_time = time.time() - parse_start
                total_time = time.time() - perception_start

                self.memory.set_context("latest_perception", perception_data)
                logger.info(f"✅ JSON解析成功 (耗时: {parse_time:.3f}s)")
                logger.info(f"🎯 感知处理完成 - 总耗时: {total_time:.3f}s")
                logger.debug(f"📊 感知结果: {str(perception_data)[:200]}...")

                return perception_data
            else:
                raise ValueError("无法解析JSON")
        except Exception as e:
            parse_time = time.time() - parse_start
            total_time = time.time() - perception_start

            logger.warning(f"⚠️ JSON解析失败，使用备用结果 (解析耗时: {parse_time:.3f}s)")
            logger.warning(f"🔍 解析错误: {str(e)}")
            logger.debug(f"📄 原始响应: {result.content[:500]}...")

            # 如果解析失败，返回基本结果
            fallback_result = {
                "user_intent": "理解用户问题",
                "key_entities": [user_input],
                "sentiment": "neutral",
                "required_tools": [],
                "context_connections": [],
                "raw_response": result.content,
                "parse_error": str(e)
            }
            self.memory.set_context("latest_perception", fallback_result)

            logger.info(f"🔄 感知处理完成(备用模式) - 总耗时: {total_time:.3f}s")
            return fallback_result