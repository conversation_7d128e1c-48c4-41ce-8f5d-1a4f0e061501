from typing import List, Dict, Any, Optional, Callable
from abc import ABC, abstractmethod
import uuid
import time

from langchain.schema import HumanMessage, SystemMessage, AIMessage

from app.core.tools import BaseTool
from app.core.memory import AgentMemory
from app.core.perception import Perception
from app.core.planning import Planning
from app.core.action import Action
from app.core.observation import Observation
from app.utils.logger import logger
from app.utils.compatibility import get_compatible_llm, print_environment_info
import config


class Agent(ABC):
    """
    通用AI Agent基类，提供Agent的基本框架
    """
    def __init__(
        self,
        name: str,
        description: str,
        system_prompt: str,
        tools: List[BaseTool] = None,
        model_name: str = config.MIDSCENE_MODEL_NAME,
        temperature: float = config.TEMPERATURE,
        max_tokens: int = config.MAX_TOKENS,
        memory_size: int = config.DEFAULT_AGENT_MEMORY_SIZE,
        callback: Optional[Callable] = None
    ):
        self.id = str(uuid.uuid4())
        self.name = name
        self.description = description
        self.system_prompt = system_prompt
        self.tools = tools or []
        self.memory = AgentMemory(buffer_size=memory_size)
        self.callback = callback
        self.created_at = time.time()
        
        # 打印环境信息，帮助调试
        print_environment_info()
        
        # 使用兼容性工具初始化LLM
        self.llm = get_compatible_llm(
            model=model_name,
            temperature=temperature,
            max_tokens=max_tokens,
            openai_api_key=config.OPENAI_API_KEY,
            openai_api_base=config.OPENAI_BASE_URL,
            streaming=True  # 启用流式支持
        )
        
        # 初始化核心组件
        self.perception = Perception(self.llm, self.memory)
        self.planning = Planning(self.llm, self.memory)
        self.action = Action(self.llm, self.memory, self.tools)
        self.observation = Observation(self.memory)
        
        # 记录系统消息
        self._add_system_message(system_prompt)

    def _add_system_message(self, content: str):
        """添加系统消息到记忆中"""
        self.memory.add_message(SystemMessage(content=content))
        
    def add_human_message(self, content: str):
        """添加用户消息到记忆中"""
        self.memory.add_message(HumanMessage(content=content))
        
    def add_ai_message(self, content: str):
        """添加AI消息到记忆中"""
        self.memory.add_message(AIMessage(content=content))
        
    def process(self, user_input: str, stream: bool = False):
        """
        处理用户输入并返回响应
        如果stream=True，则使用生成器方式流式返回响应
        """
        import time
        process_start = time.time()
        logger.info(f"🧠 Agent开始处理 - 类型: {self.__class__.__name__}, 流式: {stream}")
        logger.info(f"📝 输入内容: {user_input[:200]}{'...' if len(user_input) > 200 else ''}")

        # 添加用户输入到记忆
        memory_start = time.time()
        self.add_human_message(user_input)
        memory_time = time.time() - memory_start
        logger.info(f"💾 记忆更新完成 (耗时: {memory_time:.3f}s)")

        # 感知阶段 - 理解用户意图和上下文
        perception_start = time.time()
        logger.info(f"👁️ 开始感知阶段...")
        perception_result = self.perception.process(user_input)
        perception_time = time.time() - perception_start
        logger.info(f"✅ 感知阶段完成 (耗时: {perception_time:.3f}s)")
        logger.debug(f"🔍 感知结果: {str(perception_result)[:300]}...")

        # 规划阶段 - 决定要采取的行动
        planning_start = time.time()
        logger.info(f"🎯 开始规划阶段...")
        plan = self.planning.create_plan(perception_result)
        planning_time = time.time() - planning_start
        logger.info(f"✅ 规划阶段完成 (耗时: {planning_time:.3f}s)")
        logger.debug(f"📋 规划结果: {str(plan)[:300]}...")

        # 行动阶段 - 执行计划
        action_start = time.time()
        logger.info(f"🚀 开始行动阶段...")

        if stream:
            logger.info(f"🌊 使用流式执行模式")
            return self._stream_action_and_response(plan)
        else:
            logger.info(f"📄 使用同步执行模式")
            result = self._execute_action_and_response(plan)
            action_time = time.time() - action_start
            total_time = time.time() - process_start

            logger.info(f"✅ 行动阶段完成 (耗时: {action_time:.3f}s)")
            logger.info(f"🎉 Agent处理完成 - 总耗时: {total_time:.3f}s")
            logger.info(f"📊 阶段耗时统计 - 感知: {perception_time:.3f}s, 规划: {planning_time:.3f}s, 行动: {action_time:.3f}s")

            return result
    
    def _execute_action_and_response(self, plan: Dict[str, Any]) -> str:
        """执行动作并返回完整响应"""
        action_result = self.action.execute(plan)
        
        # 观察阶段 - 分析执行结果
        observation = self.observation.analyze(action_result)
        
        # 生成最终响应
        response = self._generate_response(observation)
        
        # 添加AI响应到记忆
        self.add_ai_message(response)
        
        return response
    
    def _stream_action_and_response(self, plan: Dict[str, Any]):
        """流式执行动作并生成响应"""
        try:
            for chunk in self.action.execute_stream(plan):
                # 流式观察和响应生成
                observation_chunk = self.observation.analyze_stream(chunk)

                # 构建响应块
                response_chunk = {
                    "response_chunk": observation_chunk.get("response_chunk", ""),
                    "is_last": observation_chunk.get("is_last", False),
                    "chunk_index": observation_chunk.get("chunk_index", 0),
                    "plan": plan
                }

                yield response_chunk

            # 最终的完整响应已添加到记忆中
            full_response = self.observation.get_last_full_response()
            if full_response:
                self.add_ai_message(full_response)
        except Exception as e:
            logger.error(f"流式响应执行错误: {str(e)}")
            # 发送错误响应
            yield {
                "response_chunk": f"处理过程中发生错误: {str(e)}",
                "is_last": True,
                "error": True,
                "plan": plan
            }
    
    def _generate_response(self, observation: Dict[str, Any]) -> str:
        """基于观察生成最终响应"""
        # 可以在子类中重写该方法以自定义响应生成逻辑
        return observation.get("response", "")

    def _generate_response_stream(self, observation_chunk: Dict[str, Any]) -> Dict[str, Any]:
        """基于观察块生成响应块"""
        return {
            "response_chunk": observation_chunk.get("response_chunk", ""),
            "is_last": observation_chunk.get("is_last", False),
            "chunk_index": observation_chunk.get("chunk_index", 0)
        }
        
    def get_memory(self) -> List[Dict[str, Any]]:
        """获取代理的记忆内容"""
        return self.memory.get_messages()
    
    def clear_memory(self):
        """清除代理的记忆"""
        self.memory.clear()
        # 重新添加系统提示
        self._add_system_message(self.system_prompt)
    
    def add_tool(self, tool: BaseTool):
        """添加工具到代理"""
        self.tools.append(tool)
        
    def remove_tool(self, tool_name: str):
        """从代理中移除工具"""
        self.tools = [tool for tool in self.tools if tool.name != tool_name]

    @abstractmethod
    def custom_action(self, action_input: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行特定于子类的自定义动作
        需要在子类中实现
        """
        pass 