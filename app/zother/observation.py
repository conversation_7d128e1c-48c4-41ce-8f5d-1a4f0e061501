from typing import Dict, Any
from app.core.memory import AgentMemory


class Observation:
    """
    观察模块：分析行动执行结果，提取关键信息
    """
    
    def __init__(self, memory: AgentMemory):
        self.memory = memory
        self._last_full_response = ""
        self._response_buffer = []
    
    def analyze(self, action_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析行动执行结果，提取关键信息
        """
        # 存储原始结果
        self.memory.set_context("last_action_result", action_result)
        
        # 提取响应
        response = action_result.get("response", "")
        self._last_full_response = response
        
        # 构建观察结果
        observation = {
            "response": response,
            "used_tool": action_result.get("used_tool", False),
        }
        
        # 如果使用了工具，添加工具相关信息
        if action_result.get("used_tool", False):
            observation["tool_name"] = action_result.get("tool_name", "")
            observation["tool_result_status"] = action_result.get("tool_result", {}).get("status", "unknown")
            
            # 根据工具执行状态增加观察
            if action_result.get("tool_result", {}).get("status") == "error":
                observation["error"] = True
                observation["error_message"] = action_result.get("tool_result", {}).get("error", "未知错误")
            else:
                observation["success"] = True
        
        # 存储观察结果
        self.memory.set_context("last_observation", observation)
        
        return observation
    
    def analyze_stream(self, action_chunk: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析流式行动执行结果的块
        """
        # 提取响应块
        response_chunk = action_chunk.get("response_chunk", "")
        
        # 添加到响应缓冲区
        self._response_buffer.append(response_chunk)
        
        # 如果是最后一块，合并完整响应
        if action_chunk.get("is_last", False):
            self._last_full_response = "".join(self._response_buffer)
            self._response_buffer = []
        
        # 构建流式观察结果
        observation_chunk = {
            "response_chunk": response_chunk,
            "is_last": action_chunk.get("is_last", False),
            "chunk_index": action_chunk.get("chunk_index", 0),
            "total_chunks": action_chunk.get("total_chunks", 1)
        }
        
        return observation_chunk
    
    def get_last_full_response(self) -> str:
        """
        获取最后一次完整的响应
        """
        return self._last_full_response 