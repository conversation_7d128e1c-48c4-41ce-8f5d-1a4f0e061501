# 已废弃的自定义模块

本文件夹包含了从自定义 PPAO 循环架构迁移到 LangChain 标准 Agent 架构过程中被替代的模块。

## 迁移时间

**2025-06-12** - 从自定义 PPAO 循环升级到 LangChain ReAct 模式

## 已移动的文件

### 核心模块 (app/core/)

- **`agent.py`** - 原始的 Agent 基类，已被`langchain_agent.py`替代
- **`perception.py`** - 感知模块，已被 ReAct 的 Thought 阶段替代
- **`planning.py`** - 规划模块，已被 ReAct 的 Thought 阶段替代
- **`action.py`** - 行动模块，已被 ReAct 的 Action 阶段替代
- **`observation.py`** - 观察模块，已被 ReAct 的 Observation 阶段替代

### 文档 (docs/)

- **`agent-architecture.md`** - 旧的 PPAO 架构文档，已被`langchain-agent-architecture.md`替代

## 架构变化对比

### 旧架构 (PPAO 循环)

```
用户输入 → 感知 → 规划 → 行动 → 观察 → 响应输出
    ↑                                    ↓
    ←←←←←←←← 记忆存储 ←←←←←←←←←←←←←←←←←
```

### 新架构 (LangChain ReAct)

```
用户输入 → 思考 → 行动 → 观察 → 思考 → 行动 → ... → 最终答案
         ↑                                              ↓
         ←←←←←←← AgentExecutor 自动循环 ←←←←←←←←←←←←←←←←←
```

## 功能映射

| 旧模块           | 新实现               | 说明                                 |
| ---------------- | -------------------- | ------------------------------------ |
| `agent.py`       | `langchain_agent.py` | 使用 LangChain 的 create_react_agent |
| `perception.py`  | ReAct Thought        | 智能推理和分析                       |
| `planning.py`    | ReAct Thought        | 制定行动计划                         |
| `action.py`      | ReAct Action         | 执行具体行动                         |
| `observation.py` | ReAct Observation    | 观察和分析结果                       |

## 保留的模块

以下模块仍在使用，因为它们与新架构兼容：

- **`tools.py`** - 工具基类，被`langchain_tools.py`适配器使用
- **`memory.py`** - 记忆管理，已完全迁移到 LangChain 标准记忆功能

## 升级优势

1. **标准化**: 使用 LangChain 行业标准框架
2. **智能推理**: ReAct 模式提供真正的推理能力
3. **自动循环**: AgentExecutor 自动管理思考-行动循环
4. **工具选择**: 智能选择最合适的工具
5. **错误处理**: 内置的错误处理和重试机制
6. **生态兼容**: 兼容 LangChain 生态系统

## 注意事项

- 这些文件仅作为历史记录保存
- 不建议在新开发中使用这些模块
- 如需参考旧实现，可以查看这些文件
- 新的 Agent 实现在`app/core/langchain_agent.py`中

## 如果需要回滚

如果因为某种原因需要回滚到旧架构：

1. 将这些文件移回`app/core/`目录
2. 修改 Agent 实现以使用旧的基类
3. 更新导入语句
4. 恢复旧的文档

但强烈建议使用新的 LangChain 架构，因为它提供了更好的功能和维护性。
