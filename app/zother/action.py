from typing import Dict, Any, List, Generator
from langchain.chat_models.base import BaseChatModel

from langchain.schema import SystemMessage, HumanMessage

from app.core.memory import AgentMemory
from app.core.tools import BaseTool
from app.utils.json_fixer import fix_and_parse_json


class Action:
    """
    行动模块：执行规划创建的计划
    """
    
    def __init__(self, llm: BaseChatModel, memory: AgentMemory, tools: List[BaseTool]):
        self.llm = llm
        self.memory = memory
        self.tools = {tool.name: tool for tool in tools} if tools else {}
        
        # 增强的系统提示，强调JSON格式
        self.tool_selection_prompt = """你是一个工具选择和执行专家。根据给定的计划和可用工具，决定使用哪个工具以及如何使用它。

返回JSON格式，必须包含以下字段:
- reasoning: 选择理由
- tool: 工具名称，如果不需要工具则为null
- parameters: 工具参数对象（如果选择了工具）
- response: 直接回复（如果不使用工具）

严格以JSON格式返回，不要有任何其他文本。例如:
{
  "reasoning": "用户需要搜索信息",
  "tool": "search_tool",
  "parameters": {
    "query": "搜索关键词",
    "limit": 5
  }
}

或者不使用工具:
{
  "reasoning": "不需要工具，可以直接回答",
  "tool": null,
  "response": "这是对用户的直接回复"
}"""
        
        self.response_prompt = """你是一个高级AI助手。根据用户输入、执行计划和工具执行结果，生成最终的响应。

直接解决用户的问题或需求，清晰、有帮助、友好。不要解释你如何使用了工具或执行了计划，直接提供用户需要的信息。"""
    
    def execute(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行规划，根据需要调用工具，并生成响应
        """
        # 获取最近的用户输入
        user_messages = [msg for msg in self.memory.get_messages() if msg.type == "human"]
        user_input = user_messages[-1].content if user_messages else ""
        
        # 生成工具描述
        tool_descriptions = "\n".join([
            f"- {name}: {tool.description}" for name, tool in self.tools.items()
        ])
        
        # 决定是否使用工具以及使用哪个工具
        if not self.tools:
            # 如果没有可用工具，直接生成响应
            response_result = self._generate_response(user_input, plan, {})
            return {
                "used_tool": False,
                "response": response_result,
                "tool_result": None,
                "plan": plan
            }
        
        # 准备消息
        messages = [
            SystemMessage(content=self.tool_selection_prompt),
            HumanMessage(content=f"计划: {str(plan)}\n\n用户输入: {user_input}\n\n可用工具:\n{tool_descriptions}")
        ]
        
        # 直接调用LLM
        import time
        from app.utils.logger import logger

        llm_start = time.time()
        logger.info(f"🤖 行动模块开始调用LLM进行工具选择...")
        selection_result = self.llm(messages)
        llm_time = time.time() - llm_start
        logger.info(f"✅ 工具选择LLM调用完成 - 响应长度: {len(selection_result.content)} 字符 (耗时: {llm_time:.3f}s)")
        
        # 解析工具选择结果
        try:
            # 使用json_fixer解析结果
            selection_data = fix_and_parse_json(selection_result.content)
            if not selection_data:
                raise ValueError("无法解析JSON")
            
            # 检查是否需要使用工具
            if selection_data.get("tool") is None:
                # 不需要使用工具，直接返回响应
                direct_response = selection_data.get("response", "")
                return {
                    "used_tool": False,
                    "response": direct_response,
                    "tool_result": None,
                    "plan": plan
                }
            
            # 使用选定的工具
            tool_name = selection_data.get("tool")
            parameters = selection_data.get("parameters", {})
            
            if tool_name in self.tools:
                # 执行工具
                tool_result = self.tools[tool_name].run(**parameters)
                # 生成最终响应
                response = self._generate_response(user_input, plan, tool_result)
                
                return {
                    "used_tool": True,
                    "tool_name": tool_name,
                    "tool_parameters": parameters,
                    "tool_result": tool_result,
                    "response": response,
                    "plan": plan
                }
            else:
                # 工具不存在
                error_response = f"未找到工具: {tool_name}。请使用可用的工具。"
                return {
                    "used_tool": False,
                    "error": f"Tool not found: {tool_name}",
                    "response": error_response,
                    "plan": plan
                }
        except Exception as e:
            # 解析失败，生成错误响应
            error_response = f"无法处理您的请求: {str(e)}"
            return {
                "used_tool": False,
                "error": str(e),
                "response": error_response,
                "raw_selection": selection_result.content,
                "plan": plan
            }
    
    def execute_stream(self, plan: Dict[str, Any]) -> Generator[Dict[str, Any], None, None]:
        """
        流式执行规划，生成响应流
        """
        try:
            # 获取最近的用户输入
            user_messages = [msg for msg in self.memory.get_messages() if msg.type == "human"]
            user_input = user_messages[-1].content if user_messages else ""

            # 检查是否需要使用工具
            if not self.tools:
                # 没有工具，直接生成流式响应
                yield from self._generate_stream_response(user_input, plan, {})
                return

            # 决定是否使用工具
            tool_descriptions = "\n".join([
                f"- {name}: {tool.description}" for name, tool in self.tools.items()
            ])

            messages = [
                SystemMessage(content=self.tool_selection_prompt),
                HumanMessage(content=f"计划: {str(plan)}\n\n用户输入: {user_input}\n\n可用工具:\n{tool_descriptions}")
            ]

            # 调用LLM决定工具使用
            selection_result = self.llm(messages)

            try:
                # 解析工具选择结果
                selection_data = fix_and_parse_json(selection_result.content)

                if selection_data and selection_data.get("use_tool", False):
                    tool_name = selection_data.get("tool_name", "")
                    parameters = selection_data.get("parameters", {})

                    if tool_name in self.tools:
                        # 执行工具
                        tool_result = self.tools[tool_name].run(**parameters)
                        # 生成流式响应
                        yield from self._generate_stream_response(user_input, plan, tool_result)
                    else:
                        # 工具不存在，生成错误响应
                        error_msg = f"未找到工具: {tool_name}"
                        yield from self._generate_error_stream(error_msg, plan)
                else:
                    # 不使用工具，直接生成响应
                    yield from self._generate_stream_response(user_input, plan, {})

            except Exception as e:
                # 解析失败，生成错误响应
                error_msg = f"工具选择解析失败: {str(e)}"
                yield from self._generate_error_stream(error_msg, plan)

        except Exception as e:
            # 整体执行失败
            error_msg = f"执行过程中发生错误: {str(e)}"
            yield from self._generate_error_stream(error_msg, plan)
    
    def _generate_response(self, user_input: str, plan: Dict[str, Any], tool_result: Dict[str, Any]) -> str:
        """
        根据用户输入、计划和工具结果生成最终响应
        """
        messages = [
            SystemMessage(content=self.response_prompt),
            HumanMessage(content=f"用户输入: {user_input}\n\n执行计划: {str(plan)}\n\n工具执行结果: {str(tool_result)}")
        ]

        import time
        from app.utils.logger import logger

        llm_start = time.time()
        logger.info(f"🤖 行动模块开始调用LLM生成最终响应...")
        result = self.llm(messages)
        llm_time = time.time() - llm_start
        logger.info(f"✅ 最终响应LLM调用完成 - 响应长度: {len(result.content)} 字符 (耗时: {llm_time:.3f}s)")

        return result.content

    def _generate_stream_response(self, user_input: str, plan: Dict[str, Any], tool_result: Dict[str, Any]) -> Generator[Dict[str, Any], None, None]:
        """
        生成流式响应
        """
        messages = [
            SystemMessage(content=self.response_prompt),
            HumanMessage(content=f"用户输入: {user_input}\n\n执行计划: {str(plan)}\n\n工具执行结果: {str(tool_result)}")
        ]

        # 使用流式模式调用LLM
        import time
        from app.utils.logger import logger

        if hasattr(self.llm, "stream"):
            stream_start = time.time()
            logger.info(f"🌊 行动模块开始流式LLM调用...")
            chunk_index = 0
            response_buffer = ""
            first_chunk_time = None

            for chunk in self.llm.stream(messages):
                if chunk_index == 0:
                    first_chunk_time = time.time()
                    logger.info(f"🎯 收到第一个响应块 (延迟: {first_chunk_time - stream_start:.3f}s)")
                if hasattr(chunk, 'content') and chunk.content:
                    chunk_text = chunk.content
                    response_buffer += chunk_text

                    yield {
                        "response_chunk": chunk_text,
                        "chunk_index": chunk_index,
                        "is_last": False,
                        "plan": plan
                    }

                    chunk_index += 1

            # 发送最后一个块，标记完成
            total_stream_time = time.time() - stream_start
            logger.info(f"✅ 流式LLM调用完成 - 总块数: {chunk_index}, 总耗时: {total_stream_time:.3f}s")

            yield {
                "response_chunk": "",
                "chunk_index": chunk_index,
                "is_last": True,
                "total_chunks": chunk_index,
                "full_response": response_buffer,
                "plan": plan
            }
        else:
            # 如果LLM不支持流式输出，生成完整响应后分块发送
            result = self.llm(messages)
            response = result.content

            # 按句子分割模拟流式输出
            sentences = response.split(". ")
            for i, sentence in enumerate(sentences):
                if i < len(sentences) - 1:
                    sentence += ". "

                yield {
                    "response_chunk": sentence,
                    "chunk_index": i,
                    "total_chunks": len(sentences),
                    "is_last": i == len(sentences) - 1,
                    "plan": plan
                }

    def _generate_error_stream(self, error_msg: str, plan: Dict[str, Any]) -> Generator[Dict[str, Any], None, None]:
        """
        生成错误流式响应
        """
        yield {
            "response_chunk": error_msg,
            "chunk_index": 0,
            "is_last": True,
            "total_chunks": 1,
            "error": True,
            "plan": plan
        }