from typing import Dict, Any, List
from langchain.chat_models.base import BaseChatModel
from langchain.prompts import ChatPromptTemplate
from langchain.schema import SystemMessage, HumanMessage

from app.core.memory import AgentMemory
from app.utils.json_fixer import fix_and_parse_json


class Planning:
    """
    规划模块：根据感知结果创建行动计划
    """
    
    def __init__(self, llm: BaseChatModel, memory: AgentMemory):
        self.llm = llm
        self.memory = memory
        
        # 增强的系统提示，强调JSON格式
        self.system_prompt = """你是一个高级规划模块。根据感知分析结果创建行动计划，并以JSON格式返回。

必须包含以下字段:
- goal: 主要目标
- steps: 完成目标所需的步骤列表
- tools: 可能使用的工具列表
- priorities: 步骤优先级映射
- alternatives: 备选方案映射

严格以JSON格式返回，不要有任何其他文本。例如:
{
  "goal": "回答用户问题",
  "steps": ["理解问题", "查找信息", "生成回答"],
  "tools": ["搜索工具", "知识库"],
  "priorities": {"理解问题": "高", "查找信息": "中", "生成回答": "高"},
  "alternatives": {"查找信息": "使用本地数据"}
}"""
    
    def create_plan(self, perception_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        根据感知结果创建行动计划
        """
        import time
        from app.utils.logger import logger

        planning_start = time.time()
        logger.info(f"🎯 规划模块开始处理...")

        # 获取最近的对话历史
        history_start = time.time()
        recent_history = self.memory.get_history_as_string()
        history_time = time.time() - history_start
        logger.info(f"📚 获取对话历史完成 - 历史长度: {len(recent_history)} 字符 (耗时: {history_time:.3f}s)")

        # 准备消息
        message_prep_start = time.time()
        messages = [
            SystemMessage(content=self.system_prompt),
            HumanMessage(content=f"感知分析结果: {str(perception_data)}\n\n历史对话上下文:\n{recent_history}")
        ]
        message_prep_time = time.time() - message_prep_start
        logger.info(f"📝 消息准备完成 - 消息数: {len(messages)} (耗时: {message_prep_time:.3f}s)")

        # 直接调用LLM
        llm_start = time.time()
        logger.info(f"🤖 开始调用LLM进行规划分析...")
        result = self.llm(messages)
        llm_time = time.time() - llm_start
        logger.info(f"✅ LLM调用完成 - 响应长度: {len(result.content)} 字符 (耗时: {llm_time:.3f}s)")

        # 解析并返回规划结果
        parse_start = time.time()
        try:
            # 尝试解析JSON结果，使用json_fixer
            logger.info(f"🔧 开始解析JSON结果...")
            plan_data = fix_and_parse_json(result.content)
            if plan_data:
                parse_time = time.time() - parse_start
                total_time = time.time() - planning_start

                self.memory.set_context("latest_plan", plan_data)
                logger.info(f"✅ JSON解析成功 (耗时: {parse_time:.3f}s)")
                logger.info(f"🎯 规划处理完成 - 总耗时: {total_time:.3f}s")
                logger.debug(f"📋 规划结果: {str(plan_data)[:200]}...")

                return plan_data
            else:
                raise ValueError("无法解析JSON")
        except Exception as e:
            parse_time = time.time() - parse_start
            total_time = time.time() - planning_start

            logger.warning(f"⚠️ JSON解析失败，使用备用计划 (解析耗时: {parse_time:.3f}s)")
            logger.warning(f"🔍 解析错误: {str(e)}")
            logger.debug(f"📄 原始响应: {result.content[:500]}...")

            # 如果解析失败，返回基本计划
            fallback_plan = {
                "goal": perception_data.get("user_intent", "完成用户请求"),
                "steps": ["理解用户需求", "提供相应回复"],
                "tools": [],
                "priorities": {"理解用户需求": "高", "提供相应回复": "高"},
                "alternatives": {},
                "raw_response": result.content,
                "parse_error": str(e)
            }
            self.memory.set_context("latest_plan", fallback_plan)

            logger.info(f"🔄 规划处理完成(备用模式) - 总耗时: {total_time:.3f}s")
            return fallback_plan