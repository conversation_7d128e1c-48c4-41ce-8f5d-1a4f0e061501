"""
基于<PERSON><PERSON><PERSON>n标准Agent的智能体实现
使用create_react_agent和AgentExecutor
"""

from typing import List, Dict, Any, Optional, Callable, Generator
import uuid
import time
from abc import ABC, abstractmethod

from langchain.agents import create_react_agent, AgentExecutor
from langchain.tools import BaseTool as LangChainBaseTool
from langchain.schema import HumanMessage, SystemMessage, AIMessage
from langchain.prompts import PromptTemplate

from langchain.callbacks.base import BaseCallbackHandler
from langchain.callbacks.manager import CallbackManager

from app.core.tools import BaseTool as CustomBaseTool
from app.core.langchain_tools import convert_custom_tools_to_langchain
from app.core.memory import AgentMemory
from app.utils.logger import get_logger
from app.utils.compatibility import get_compatible_llm
import config

logger = get_logger("langchain_agent")


class StreamingCallbackHandler(BaseCallbackHandler):
    """流式输出回调处理器"""
    
    def __init__(self, callback_func: Optional[Callable] = None):
        self.callback_func = callback_func
        self.current_response = ""
        self.chunk_index = 0
    
    def on_llm_new_token(self, token: str, **kwargs) -> None:
        """处理新的token"""
        if self.callback_func:
            self.current_response += token
            self.callback_func({
                "response_chunk": token,
                "chunk_index": self.chunk_index,
                "is_last": False,
                "full_response": self.current_response
            })
            self.chunk_index += 1
    
    def on_llm_end(self, response, **kwargs) -> None:
        """LLM结束时调用"""
        if self.callback_func:
            self.callback_func({
                "response_chunk": "",
                "chunk_index": self.chunk_index,
                "is_last": True,
                "full_response": self.current_response,
                "total_chunks": self.chunk_index
            })


class LangChainAgent(ABC):
    """
    基于LangChain标准Agent的智能体基类
    使用create_react_agent和AgentExecutor实现真正的感知-决策-行动循环
    """
    
    def __init__(
        self,
        name: str,
        description: str,
        system_prompt: str,
        tools: List[CustomBaseTool] = None,
        model_name: str = config.MIDSCENE_MODEL_NAME,
        temperature: float = config.TEMPERATURE,
        max_tokens: int = config.MAX_TOKENS,
        memory_size: int = config.DEFAULT_AGENT_MEMORY_SIZE,
        max_iterations: int = 5,
        callback: Optional[Callable] = None
    ):
        self.id = str(uuid.uuid4())
        self.name = name
        self.description = description
        self.system_prompt = system_prompt
        self.callback = callback
        self.created_at = time.time()
        self.max_iterations = max_iterations
        
        logger.info(f"🤖 初始化LangChain Agent: {name}")
        
        # 初始化LLM
        self.llm = get_compatible_llm(
            model=model_name,
            temperature=temperature,
            max_tokens=max_tokens,
            openai_api_key=config.OPENAI_API_KEY,
            openai_api_base=config.OPENAI_BASE_URL,
            streaming=True
        )
        
        # 转换工具为LangChain格式
        self.custom_tools = tools or []
        self.langchain_tools = convert_custom_tools_to_langchain(self.custom_tools)
        
        # 创建统一的记忆系统 - 完全使用langchain.memory标准功能
        self.memory = AgentMemory(buffer_size=memory_size)
        # 直接使用AgentMemory中的langchain记忆对象，避免重复
        self.langchain_memory = self.memory.get_langchain_memory()
        
        # 使用标准的ReAct提示模板
        self.prompt_template = self._create_standard_react_prompt()

        # 创建Agent和Executor
        self._create_agent_executor()
        
        # 记录系统消息
        self._add_system_message(system_prompt)
        
        logger.info(f"✅ LangChain Agent初始化完成: {name}, 工具数量: {len(self.langchain_tools)}")
    
    def _create_standard_react_prompt(self) -> PromptTemplate:
        """创建标准的ReAct提示模板"""
        # 使用LangChain标准的ReAct提示模板格式
        template = """Answer the following questions as best you can. You have access to the following tools:

{tools}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Begin!

Question: {input}
Thought:{agent_scratchpad}"""

        # 创建提示模板，必须包含所有必需的变量
        return PromptTemplate(
            template=template,
            input_variables=["input", "agent_scratchpad", "tools", "tool_names"]
        )

    def _create_prompt_template(self) -> PromptTemplate:
        """创建ReAct提示模板"""
        template = f"""你是{self.name}。{self.system_prompt}

你有以下工具可以使用:
{{tools}}

使用以下格式进行思考和行动:

Question: 用户的输入问题
Thought: 你应该思考要做什么
Action: 要采取的行动，应该是[{{tool_names}}]中的一个
Action Input: 行动的输入
Observation: 行动的结果
... (这个思考/行动/观察可以重复N次)
Thought: 我现在知道最终答案了
Final Answer: 对原始问题的最终答案

开始!

{{chat_history}}

Question: {{input}}
Thought: {{agent_scratchpad}}"""

        # 生成工具描述
        tools_desc = "\n".join([f"{tool.name}: {tool.description}" for tool in self.langchain_tools])
        tool_names = ", ".join([tool.name for tool in self.langchain_tools])

        return PromptTemplate(
            template=template,
            input_variables=["input", "chat_history", "agent_scratchpad"],
            partial_variables={
                "tools": tools_desc,
                "tool_names": tool_names
            }
        )
    
    def _create_agent_executor(self):
        """创建Agent和Executor"""
        try:
            # 创建ReAct Agent
            self.agent = create_react_agent(
                llm=self.llm,
                tools=self.langchain_tools,
                prompt=self.prompt_template
            )
            
            # 创建Agent Executor
            self.agent_executor = AgentExecutor.from_agent_and_tools(
                agent=self.agent,
                tools=self.langchain_tools,
                verbose=True,
                max_iterations=self.max_iterations,
                handle_parsing_errors=True,
                return_intermediate_steps=True
            )
            
            logger.info(f"✅ Agent Executor创建成功")
            
        except Exception as e:
            logger.error(f"❌ 创建Agent Executor失败: {str(e)}")
            raise
    
    def _add_system_message(self, content: str):
        """添加系统消息到记忆中"""
        self.memory.add_message(SystemMessage(content=content))
    
    def add_human_message(self, content: str):
        """添加用户消息到记忆中"""
        self.memory.add_message(HumanMessage(content=content))
    
    def add_ai_message(self, content: str):
        """添加AI消息到记忆中"""
        self.memory.add_message(AIMessage(content=content))
    
    def process(self, user_input: str, stream: bool = False):
        """
        处理用户输入并返回响应
        使用LangChain的Agent Executor进行真正的感知-决策-行动循环
        """
        start_time = time.time()
        logger.info(f"🧠 LangChain Agent开始处理 - 类型: {self.__class__.__name__}, 流式: {stream}")
        logger.info(f"📝 输入内容: {user_input[:200]}{'...' if len(user_input) > 200 else ''}")
        
        # 添加用户输入到记忆
        self.add_human_message(user_input)
        
        try:
            if stream:
                return self._process_stream(user_input)
            else:
                return self._process_sync(user_input)
                
        except Exception as e:
            error_msg = f"处理过程中发生错误: {str(e)}"
            logger.error(f"💥 Agent处理失败: {error_msg}")
            
            if stream:
                return self._generate_error_stream(error_msg)
            else:
                return {
                    "response": error_msg,
                    "error": True,
                    "agent_id": self.id
                }
        finally:
            total_time = time.time() - start_time
            logger.info(f"🎉 Agent处理完成 - 总耗时: {total_time:.3f}s")
    
    def _process_sync(self, user_input: str) -> Dict[str, Any]:
        """同步处理"""
        try:
            # 使用Agent Executor执行，只传入input
            # tools和tool_names已经在创建Agent时通过提示模板处理了
            result = self.agent_executor.invoke({"input": user_input})
            
            response = result.get("output", "")
            intermediate_steps = result.get("intermediate_steps", [])
            
            # 添加AI响应到记忆
            self.add_ai_message(response)
            
            return {
                "response": response,
                "intermediate_steps": intermediate_steps,
                "agent_id": self.id,
                "used_tools": len(intermediate_steps) > 0,
                "tool_calls": [step[0].tool for step in intermediate_steps if hasattr(step[0], 'tool')]
            }
            
        except Exception as e:
            error_msg = f"Agent执行失败: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return {
                "response": error_msg,
                "error": True,
                "agent_id": self.id
            }
    
    def _process_stream(self, user_input: str) -> Generator[Dict[str, Any], None, None]:
        """流式处理"""
        try:
            # 创建流式回调
            def stream_callback(chunk_data):
                if hasattr(self, '_current_stream_callback'):
                    self._current_stream_callback(chunk_data)
            
            streaming_handler = StreamingCallbackHandler(stream_callback)
            callback_manager = CallbackManager([streaming_handler])
            
            # 设置回调管理器
            self.agent_executor.callbacks = callback_manager
            
            # 执行并流式返回结果
            chunk_index = 0
            response_buffer = ""
            
            def stream_generator():
                nonlocal chunk_index, response_buffer
                
                def chunk_callback(chunk_data):
                    nonlocal chunk_index, response_buffer
                    response_buffer += chunk_data.get("response_chunk", "")
                    yield {
                        "response_chunk": chunk_data.get("response_chunk", ""),
                        "chunk_index": chunk_index,
                        "is_last": chunk_data.get("is_last", False),
                        "agent_id": self.id
                    }
                    chunk_index += 1
                
                self._current_stream_callback = chunk_callback
                
                try:
                    result = self.agent_executor.invoke({
                        "input": user_input,
                        "chat_history": self.memory.get_history_as_string()
                    })
                    
                    # 确保发送最后一个块
                    yield {
                        "response_chunk": "",
                        "chunk_index": chunk_index,
                        "is_last": True,
                        "full_response": response_buffer,
                        "agent_id": self.id,
                        "intermediate_steps": result.get("intermediate_steps", [])
                    }
                    
                    # 添加完整响应到记忆
                    self.add_ai_message(response_buffer)
                    
                except Exception as e:
                    yield {
                        "response_chunk": f"执行错误: {str(e)}",
                        "chunk_index": chunk_index,
                        "is_last": True,
                        "error": True,
                        "agent_id": self.id
                    }
            
            return stream_generator()
            
        except Exception as e:
            return self._generate_error_stream(f"流式处理失败: {str(e)}")
    
    def _generate_error_stream(self, error_msg: str) -> Generator[Dict[str, Any], None, None]:
        """生成错误流式响应"""
        yield {
            "response_chunk": error_msg,
            "chunk_index": 0,
            "is_last": True,
            "error": True,
            "agent_id": self.id
        }
    
    def get_memory(self) -> List[Dict[str, Any]]:
        """获取代理的记忆内容"""
        return self.memory.get_messages()
    
    def clear_memory(self):
        """清除代理的记忆"""
        self.memory.clear()
        # 重新添加系统提示
        self._add_system_message(self.system_prompt)
    
    def add_tool(self, tool: CustomBaseTool):
        """添加工具到代理"""
        self.custom_tools.append(tool)
        # 重新转换工具并重建Agent
        self.langchain_tools = convert_custom_tools_to_langchain(self.custom_tools)
        self._create_agent_executor()
        logger.info(f"✅ 添加工具: {tool.name}")
