"""
LangChain工具适配器
将现有的BaseTool转换为LangChain Tool格式
"""

from typing import Dict, Any, List, Optional, Type
from langchain.tools import BaseTool as LangChainBaseTool
from langchain.callbacks.manager import CallbackManagerForToolRun
from pydantic import BaseModel, Field
import inspect

from app.core.tools import BaseTool as CustomBaseTool
from app.utils.logger import get_logger

logger = get_logger("langchain_tools")


class LangChainToolAdapter(LangChainBaseTool):
    """
    将自定义BaseTool适配为LangChain Tool
    """
    
    name: str = Field(description="工具名称")
    description: str = Field(description="工具描述")
    custom_tool: CustomBaseTool = Field(description="自定义工具实例")
    
    def __init__(self, custom_tool: CustomBaseTool, **kwargs):
        super().__init__(
            name=custom_tool.name,
            description=custom_tool.description,
            custom_tool=custom_tool,
            **kwargs
        )
    
    def _run(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
        **kwargs
    ) -> str:
        """执行工具"""
        try:
            logger.info(f"🔧 执行LangChain适配工具: {self.name}")
            logger.debug(f"📝 工具参数: query={query}, kwargs={kwargs}")
            
            # 获取自定义工具的_run方法签名
            sig = inspect.signature(self.custom_tool._run)
            params = {}
            
            # 如果方法只接受一个参数，直接传入query
            if len(sig.parameters) == 1:
                param_name = list(sig.parameters.keys())[0]
                params[param_name] = query
            else:
                # 如果有多个参数，尝试从kwargs中获取
                for param_name, param in sig.parameters.items():
                    if param_name in kwargs:
                        params[param_name] = kwargs[param_name]
                    elif param_name == "query" or param_name == "input":
                        params[param_name] = query
                    elif param.default != inspect.Parameter.empty:
                        # 使用默认值
                        params[param_name] = param.default
            
            # 执行自定义工具
            result = self.custom_tool.run(**params)
            
            # 处理结果
            if isinstance(result, dict):
                if result.get("status") == "success":
                    tool_result = result.get("result", "")
                    logger.info(f"✅ 工具执行成功: {self.name}")
                    return str(tool_result)
                else:
                    error_msg = result.get("error", "工具执行失败")
                    logger.error(f"❌ 工具执行失败: {self.name} - {error_msg}")
                    return f"工具执行失败: {error_msg}"
            else:
                logger.info(f"✅ 工具执行成功: {self.name}")
                return str(result)
                
        except Exception as e:
            error_msg = f"工具执行异常: {str(e)}"
            logger.error(f"💥 工具执行异常: {self.name} - {error_msg}")
            return error_msg
    
    async def _arun(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
        **kwargs
    ) -> str:
        """异步执行工具（暂时使用同步实现）"""
        return self._run(query, run_manager, **kwargs)


def convert_custom_tools_to_langchain(custom_tools: List[CustomBaseTool]) -> List[LangChainBaseTool]:
    """
    将自定义工具列表转换为LangChain工具列表
    """
    langchain_tools = []
    
    for custom_tool in custom_tools:
        try:
            adapter = LangChainToolAdapter(custom_tool)
            langchain_tools.append(adapter)
            logger.info(f"✅ 成功转换工具: {custom_tool.name}")
        except Exception as e:
            logger.error(f"❌ 转换工具失败: {custom_tool.name} - {str(e)}")
    
    logger.info(f"🔧 工具转换完成: {len(langchain_tools)}/{len(custom_tools)} 个工具转换成功")
    return langchain_tools


class DynamicToolSchema(BaseModel):
    """动态工具Schema"""
    pass


def create_dynamic_tool_from_custom(custom_tool: CustomBaseTool) -> LangChainBaseTool:
    """
    从自定义工具创建动态LangChain工具
    """
    # 获取工具方法签名
    sig = inspect.signature(custom_tool._run)
    
    # 创建动态Schema类
    schema_fields = {}
    for param_name, param in sig.parameters.items():
        param_type = str
        if param.annotation != inspect.Parameter.empty:
            param_type = param.annotation
        
        default_value = ... if param.default == inspect.Parameter.empty else param.default
        schema_fields[param_name] = (param_type, Field(default=default_value, description=f"参数 {param_name}"))
    
    # 创建动态Schema类
    DynamicSchema = type(f"{custom_tool.name}Schema", (BaseModel,), schema_fields)
    
    class DynamicTool(LangChainBaseTool):
        name = custom_tool.name
        description = custom_tool.description
        args_schema: Type[BaseModel] = DynamicSchema
        
        def _run(self, run_manager: Optional[CallbackManagerForToolRun] = None, **kwargs) -> str:
            try:
                result = custom_tool.run(**kwargs)
                if isinstance(result, dict):
                    if result.get("status") == "success":
                        return str(result.get("result", ""))
                    else:
                        return f"工具执行失败: {result.get('error', '未知错误')}"
                return str(result)
            except Exception as e:
                return f"工具执行异常: {str(e)}"
        
        async def _arun(self, run_manager: Optional[CallbackManagerForToolRun] = None, **kwargs) -> str:
            return self._run(run_manager, **kwargs)
    
    return DynamicTool()
