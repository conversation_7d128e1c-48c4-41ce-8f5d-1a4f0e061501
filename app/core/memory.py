from typing import List, Dict, Any
from langchain.schema import BaseMessage, SystemMessage
from langchain.memory import ConversationBufferWindowMemory
from langchain.memory.chat_message_histories import ChatMessageHistory
import config


class AgentMemory:
    """
    Agent记忆模块，完全基于LangChain标准记忆功能
    直接使用ConversationBufferWindowMemory，提供简洁的API接口
    """

    def __init__(self, buffer_size: int = config.DEFAULT_AGENT_MEMORY_SIZE):
        self.buffer_size = buffer_size
        self.context: Dict[str, Any] = {}  # 存储额外上下文信息

        # 直接使用LangChain的ConversationBufferWindowMemory作为主要记忆系统
        self.memory = ConversationBufferWindowMemory(
            k=buffer_size,
            memory_key="chat_history",
            return_messages=True,
            chat_memory=ChatMessageHistory()
        )

        # 系统消息单独存储，避免被窗口机制清除
        self.system_messages: List[BaseMessage] = []

    def add_message(self, message: BaseMessage):
        """添加消息到记忆"""
        if message.type == "system":
            # 系统消息单独存储，避免被窗口机制清除
            self.system_messages.append(message)
        else:
            # 其他消息直接使用LangChain的记忆管理
            self.memory.chat_memory.add_message(message)

    def get_messages(self) -> List[BaseMessage]:
        """获取所有消息，包括系统消息和对话历史"""
        # 合并系统消息和对话历史
        chat_messages = self.memory.chat_memory.messages
        return self.system_messages + chat_messages

    def get_last_n_messages(self, n: int) -> List[BaseMessage]:
        """获取最近的n条消息"""
        all_messages = self.get_messages()
        return all_messages[-n:] if n < len(all_messages) else all_messages

    def clear(self):
        """清除所有消息，保留系统消息"""
        # 清除LangChain记忆中的对话历史
        self.memory.clear()
        # 系统消息保持不变

    def set_context(self, key: str, value: Any):
        """设置上下文信息"""
        self.context[key] = value

    def get_context(self, key: str, default: Any = None) -> Any:
        """获取上下文信息"""
        return self.context.get(key, default)

    def clear_context(self):
        """清除上下文信息"""
        self.context = {}

    def get_history_as_string(self) -> str:
        """将历史记录转换为字符串表示"""
        result = ""
        for message in self.get_messages():
            prefix = f"{message.type}: "
            content = message.content
            result += f"{prefix}{content}\n\n"
        return result

    def get_memory_stats(self) -> Dict[str, Any]:
        """获取记忆统计信息"""
        all_messages = self.get_messages()
        message_types = {}
        for message in all_messages:
            message_type = message.type
            if message_type in message_types:
                message_types[message_type] += 1
            else:
                message_types[message_type] = 1

        return {
            "total_messages": len(all_messages),
            "message_types": message_types,
            "buffer_size": self.buffer_size,
            "context_keys": list(self.context.keys())
        }

    def get_langchain_memory(self) -> ConversationBufferWindowMemory:
        """获取底层的LangChain记忆对象，用于与LangChain Agent集成"""
        return self.memory

    def save_context(self, inputs: Dict[str, Any], outputs: Dict[str, str]):
        """保存对话上下文到LangChain记忆中"""
        self.memory.save_context(inputs, outputs)

    def load_memory_variables(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """从LangChain记忆中加载变量"""
        return self.memory.load_memory_variables(inputs)