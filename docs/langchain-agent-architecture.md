# LangChain Agent 架构详解

## 概述

本项目已从自定义的 **感知-规划-行动-观察** (PPAO) 循环架构升级为基于 **LangChain 标准 Agent** 的架构。新架构使用 `create_react_agent` 和 `AgentExecutor` 实现真正的 **感知-决策-行动** 循环，具备更强的推理能力和更标准的工具调用机制。

## 核心设计理念

### 1. ReAct 模式架构

```
用户输入 → 思考 → 行动 → 观察 → 思考 → 行动 → ... → 最终答案
    ↑                                                      ↓
    ←←←←←←←← 记忆存储 ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←
```

**ReAct = Reasoning (推理) + Acting (行动)**

### 2. 标准化设计

- **LangChain 兼容**: 使用标准的 LangChain Agent 框架
- **工具标准化**: 自动转换自定义工具为 LangChain Tool 格式
- **流式支持**: 保持原有的流式响应能力
- **记忆管理**: 双重记忆系统（自定义 + LangChain）

## 技术栈

### 核心依赖

- **LangChain**: 0.1.0+ (Agent 框架)
- **LangChain-OpenAI**: 0.0.8+ (LLM 集成)
- **LangChain-Core**: 0.1.0+ (核心功能)
- **LangChain-Experimental**: 0.0.50+ (实验性功能)

### 架构组件

- **LangChainAgent**: 新的 Agent 基类
- **AgentExecutor**: LangChain 的 Agent 执行器
- **ReAct Prompt**: 标准的推理-行动提示模板
- **Tool Adapter**: 工具适配器系统

## 核心模块详解

### 1. LangChain Agent 基类 (`app/core/langchain_agent.py`)

#### 核心职责

- 使用 `create_react_agent` 创建标准 Agent
- 管理 `AgentExecutor` 执行循环
- 提供流式响应支持
- 维护双重记忆系统

#### 关键属性

```python
class LangChainAgent:
    def __init__(self):
        self.llm = get_compatible_llm()           # 语言模型
        self.custom_tools = tools                 # 自定义工具
        self.langchain_tools = converted_tools    # LangChain 工具
        self.agent = create_react_agent(...)      # ReAct Agent
        self.agent_executor = AgentExecutor(...)  # 执行器
        self.memory = AgentMemory()               # 自定义记忆
        self.langchain_memory = ConversationBufferWindowMemory()  # LangChain 记忆
```

#### 执行流程

1. **初始化**: 创建 ReAct Agent 和 AgentExecutor
2. **处理请求**: 使用 AgentExecutor.invoke() 执行
3. **ReAct 循环**: 自动进行思考-行动-观察循环
4. **流式支持**: 通过回调机制实现流式输出

### 2. 工具适配器系统 (`app/core/langchain_tools.py`)

#### 核心职责

- 将自定义 BaseTool 转换为 LangChain Tool
- 自动生成工具 Schema
- 处理工具调用和结果
- 支持动态工具注册

#### 关键特性

```python
class LangChainToolAdapter(LangChainBaseTool):
    def __init__(self, custom_tool: CustomBaseTool):
        self.custom_tool = custom_tool
    
    def _run(self, query: str, **kwargs) -> str:
        # 自动适配参数并调用自定义工具
        result = self.custom_tool.run(**params)
        return self._format_result(result)
```

#### 转换机制

- **参数映射**: 自动分析方法签名，映射参数
- **结果处理**: 标准化工具执行结果
- **错误处理**: 统一的异常处理机制
- **Schema 生成**: 自动生成 LangChain 兼容的工具描述

### 3. ReAct 提示模板

#### 标准模板结构

```
你是{agent_name}。{system_prompt}

你有以下工具可以使用:
{tools}

使用以下格式进行思考和行动:

Question: 用户的输入问题
Thought: 你应该思考要做什么
Action: 要采取的行动，应该是[{tool_names}]中的一个
Action Input: 行动的输入
Observation: 行动的结果
... (这个思考/行动/观察可以重复N次)
Thought: 我现在知道最终答案了
Final Answer: 对原始问题的最终答案

开始!

{chat_history}

Question: {input}
Thought: {agent_scratchpad}
```

#### 模板特点

- **结构化推理**: 明确的思考-行动-观察步骤
- **工具集成**: 自动注入可用工具信息
- **历史维护**: 包含对话历史上下文
- **灵活扩展**: 支持自定义系统提示

### 4. 流式响应机制

#### 实现原理

```python
class StreamingCallbackHandler(BaseCallbackHandler):
    def on_llm_new_token(self, token: str, **kwargs):
        # 处理新的 token，实现流式输出
        self.callback_func({
            "response_chunk": token,
            "chunk_index": self.chunk_index,
            "is_last": False
        })
```

#### 流式特性

- **实时响应**: 逐 token 流式输出
- **进度跟踪**: 块索引和完成状态
- **兼容性**: 与原有 SSE 接口兼容
- **错误处理**: 流式错误处理机制

### 5. 双重记忆系统

#### 设计原理

- **自定义记忆**: 保持原有的 AgentMemory 功能
- **LangChain 记忆**: 使用 ConversationBufferWindowMemory
- **同步机制**: 两套记忆系统保持同步
- **兼容性**: 向后兼容原有 API

## Agent 实现示例

### 1. 助手 Agent (`app/agents/assistant_agent.py`)

```python
class AssistantAgent(LangChainAgent):
    def __init__(self, **kwargs):
        # ReAct 优化的系统提示
        system_prompt = """你是一个智能AI助手，具备强大的推理和行动能力。
        
        你的核心能力：
        - 理解用户问题并进行深度思考
        - 根据需要选择和使用合适的工具
        - 基于工具结果进行推理和分析
        - 提供准确、有帮助的最终答案"""
        
        super().__init__(
            system_prompt=system_prompt,
            tools=[SearchTool(), CalculatorTool(), ...],
            max_iterations=5,
            **kwargs
        )
```

### 2. 测试用例 Agent (`app/agents/testcase_agent.py`)

```python
class TestCaseAgent(LangChainAgent):
    def __init__(self, **kwargs):
        # 测试专家的 ReAct 提示
        system_prompt = """你是一个专业的软件测试用例编写专家。
        
        你将使用ReAct模式来处理测试用例编写任务：
        1. 思考：分析用户需求，确定需要做什么
        2. 行动：使用合适的工具或直接分析
        3. 观察：分析结果，决定下一步
        4. 重复：直到完成完整的测试用例编写"""
        
        super().__init__(
            system_prompt=system_prompt,
            max_iterations=8,  # 测试用例编写可能需要更多迭代
            **kwargs
        )
```

## 架构优势

### 1. 标准化优势

- **行业标准**: 使用 LangChain 行业标准框架
- **生态兼容**: 兼容 LangChain 生态系统
- **维护性**: 更好的长期维护性
- **扩展性**: 更容易集成新功能

### 2. 功能优势

- **真正的推理**: ReAct 模式提供真正的推理能力
- **智能工具选择**: 自动选择最合适的工具
- **多轮交互**: 支持复杂的多轮推理过程
- **错误恢复**: 内置的错误处理和重试机制

### 3. 性能优势

- **优化的执行**: LangChain 优化的执行引擎
- **并发支持**: 更好的并发处理能力
- **资源管理**: 智能的资源管理机制
- **缓存机制**: 内置的缓存优化

## 迁移指南

### 从 PPAO 到 ReAct

| PPAO 阶段 | ReAct 对应 | 说明 |
|-----------|------------|------|
| Perception | Thought | 理解和分析用户输入 |
| Planning | Thought | 制定行动计划 |
| Action | Action | 执行具体行动 |
| Observation | Observation | 观察和分析结果 |

### API 兼容性

- **process() 方法**: 保持相同的接口
- **流式响应**: 保持相同的数据格式
- **工具系统**: 自动适配现有工具
- **记忆管理**: 保持相同的 API

## 最佳实践

### 1. 系统提示优化

```python
# 针对 ReAct 模式优化的提示
system_prompt = """你是一个专业的AI助手。

工作原则：
- 仔细思考每个问题，分析需要什么信息
- 如果需要外部信息，主动使用可用工具
- 基于获得的信息进行逻辑推理
- 提供清晰、准确的最终答案

你将使用以下步骤处理问题：
1. 思考：分析问题，确定需要做什么
2. 行动：使用工具或直接回答
3. 观察：分析结果，决定下一步
4. 重复：直到得到完整答案"""
```

### 2. 工具设计

```python
class CustomTool(BaseTool):
    def _run(self, query: str) -> str:
        # 简单的参数设计，便于 LangChain 调用
        return self.process_query(query)
```

### 3. 迭代次数设置

- **简单任务**: max_iterations=3-5
- **复杂任务**: max_iterations=8-10
- **专业任务**: max_iterations=10+

这个新架构为构建智能对话系统提供了更强大、更标准、更可扩展的框架基础。
